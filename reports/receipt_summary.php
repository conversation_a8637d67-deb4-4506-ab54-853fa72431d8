<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/report_access_control.php';

// Check access
enforceReportAccess();

// Status color function - Updated to match getStatusBadge() in functions.php
function getStatusColor($status) {
    switch (strtolower($status)) {
        case 'open':
            return 'info';
        case 'checked':
            return 'primary';
        case 'pending':
            return 'warning';
        case 'success':
            return 'success';
        case 'rejected':
            return 'danger';
        case 'returned':
            return 'secondary';
        default:
            return 'dark';
    }
}

$database = new Database();
$db = $database->getConnection();

// Get user info
$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$user = $stmt->fetch();

if (!$user || !$user['is_active']) {
    session_destroy();
    header('Location: ../login.php');
    exit();
}

// Get filter parameters
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-t');
$status = $_GET['status'] ?? '';

// Build query
$where_conditions = ["e.withdrawal_date BETWEEN ? AND ?"];
$params = [$date_from, $date_to];

if (!empty($status)) {
    $where_conditions[] = "e.status = ?";
    $params[] = $status;
}

$where_clause = implode(' AND ', $where_conditions);

// Check if transfer_amount column exists
$stmt = $db->prepare("SHOW COLUMNS FROM expenses LIKE 'transfer_amount'");
$stmt->execute();
$transfer_amount_exists = $stmt->rowCount() > 0;

// Get receipt summary data
$transfer_amount_field = $transfer_amount_exists ? 'e.transfer_amount' : '0 as transfer_amount';

$stmt = $db->prepare("
    SELECT
        e.id as expense_id,
        e.exno,
        e.withdrawal_date,
        e.status,
        $transfer_amount_field,
        COALESCE(e.total_amount, 0) as total_amount,
        rn.receipt_number,
        rn.amount as receipt_amount,
        rn.description,
        i.name as item_name,
        c.name as customer_name,
        d.name as driver_name,
        u.full_name as created_by_name
    FROM expenses e
    LEFT JOIN receipt_numbers rn ON e.id = rn.expense_id
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    LEFT JOIN users u ON e.created_by = u.id
    WHERE $where_clause
    ORDER BY e.withdrawal_date DESC, e.exno, rn.id
");

$stmt->execute($params);
$receipt_data = $stmt->fetchAll();

// Calculate totals
$total_expenses = 0;
$total_receipts = 0;
$expense_summary = [];

foreach ($receipt_data as $row) {
    $expense_id = $row['expense_id'];
    
    if (!isset($expense_summary[$expense_id])) {
        $expense_summary[$expense_id] = [
            'exno' => $row['exno'],
            'withdrawal_date' => $row['withdrawal_date'],
            'status' => $row['status'],
            'transfer_amount' => floatval($row['transfer_amount'] ?? 0),
            'total_amount' => floatval($row['total_amount'] ?? 0),
            'item_name' => $row['item_name'],
            'customer_name' => $row['customer_name'],
            'driver_name' => $row['driver_name'],
            'created_by_name' => $row['created_by_name'],
            'receipts' => []
        ];
        $total_expenses += floatval($row['total_amount'] ?? 0);
    }
    
    if (!empty($row['receipt_number'])) {
        $expense_summary[$expense_id]['receipts'][] = [
            'receipt_number' => $row['receipt_number'],
            'amount' => $row['receipt_amount'],
            'description' => $row['description']
        ];
        $total_receipts += $row['receipt_amount'];
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt Summary Report - Expenses Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .receipt-numbers .badge {
            font-size: 0.75em;
            margin-bottom: 2px;
        }
        .table td {
            vertical-align: middle;
            white-space: nowrap;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
            white-space: nowrap;
        }
        .receipt-numbers {
            max-width: 250px;
            white-space: normal;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .table {
            min-width: 1200px;
        }
        .table td:nth-child(6) {
            white-space: normal;
            min-width: 200px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar"></i> Receipt Summary Report
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Filters -->
                        <form method="GET" class="row g-3 mb-4">
                            <div class="col-md-3">
                                <label class="form-label">Date From</label>
                                <input type="date" class="form-control" name="date_from" value="<?php echo $date_from; ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Date To</label>
                                <input type="date" class="form-control" name="date_to" value="<?php echo $date_to; ?>">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Status</label>
                                <select class="form-control" name="status">
                                    <option value="">All Status</option>
                                    <option value="open" <?php echo $status === 'open' ? 'selected' : ''; ?>>Open</option>
                                    <option value="checked" <?php echo $status === 'checked' ? 'selected' : ''; ?>>Checked</option>
                                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="success" <?php echo $status === 'success' ? 'selected' : ''; ?>>Approved</option>
                                    <option value="rejected" <?php echo $status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                    <option value="returned" <?php echo $status === 'returned' ? 'selected' : ''; ?>>Returned</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary d-block">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">&nbsp;</label>
                                <a href="receipt_summary.php" class="btn btn-outline-warning d-block" title="Reset Filters">
                                    <i class="fas fa-undo"></i>
                                </a>
                            </div>
                        </form>

                        <!-- Summary Cards -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h6>Total Expenses</h6>
                                        <h4><?php echo count($expense_summary); ?></h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h6>Total Amount</h6>
                                        <h4><?php echo number_format($total_expenses, 2); ?> บาท</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h6>Receipt Amount</h6>
                                        <h4><?php echo number_format($total_receipts, 2); ?> บาท</h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Export Options -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <small class="text-muted">
                                    Found <?php echo count($expense_summary); ?> expenses |
                                    Transfer Amount Column: <?php echo $transfer_amount_exists ? 'EXISTS' : 'MISSING'; ?>
                                </small>
                            </div>
                            <?php if (!empty($expense_summary)): ?>
                                <div class="btn-group" role="group">
                                    <a href="../api/export_csv.php?<?php echo http_build_query($_GET); ?>"
                                       class="btn btn-outline-success btn-sm">
                                        <i class="fas fa-download me-1"></i>CSV (Regular)
                                    </a>
                                    <a href="../api/export_csv_per_receipt.php?<?php echo http_build_query($_GET); ?>"
                                       class="btn btn-success btn-sm">
                                        <i class="fas fa-list me-1"></i>CSV (Per Receipt)
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Data Table -->
                        <div class="table-responsive">

                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Expense No</th>
                                        <th>Date</th>
                                        <th>Item</th>
                                        <th>Driver</th>
                                        <th>Transfer Amount</th>
                                        <th>Receipt Numbers</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($expense_summary as $expense_id => $expense): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($expense['exno']); ?></strong>
                                            </td>
                                            <td><?php echo formatDate($expense['withdrawal_date']); ?></td>
                                            <td><?php echo htmlspecialchars($expense['item_name'] ?: '-'); ?></td>
                                            <td><?php echo htmlspecialchars($expense['driver_name'] ?: '-'); ?></td>
                                            <td class="text-end">
                                                <span class="text-primary fw-bold"><?php echo number_format($expense['transfer_amount'], 2); ?></span>
                                                <small class="text-muted d-block">บาท</small>
                                            </td>
                                            <td>
                                                <?php if (!empty($expense['receipts'])): ?>
                                                    <div class="receipt-numbers">
                                                        <?php foreach ($expense['receipts'] as $index => $receipt): ?>
                                                            <div class="badge bg-light text-dark me-1 mb-1">
                                                                <?php echo htmlspecialchars($receipt['receipt_number']); ?>
                                                                <small class="text-success">
                                                                    (<?php echo number_format($receipt['amount'], 2); ?>)
                                                                </small>
                                                            </div>
                                                        <?php endforeach; ?>
                                                        <div class="mt-1">
                                                            <small class="text-muted">
                                                                Total: <span class="text-success fw-bold"><?php echo number_format($expense['total_amount'], 2); ?> บาท</span>
                                                                <?php if (abs($expense['transfer_amount'] - $expense['total_amount']) > 0.01): ?>
                                                                    <span class="text-warning">
                                                                        <i class="fas fa-exclamation-triangle"></i> Mismatch
                                                                    </span>
                                                                <?php endif; ?>
                                                            </small>
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">No receipts</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status = $expense['status'] ?? 'open';
                                                $statusColor = getStatusColor($status);
                                                ?>
                                                <span class="badge bg-<?php echo $statusColor; ?>">
                                                    <?php echo ucfirst($status); ?>
                                                </span>
                                                <!-- Debug: Status = <?php echo htmlspecialchars($status); ?>, Color = <?php echo htmlspecialchars($statusColor); ?> -->
                                            </td>
                                            <td>
                                                <a href="../expenses/view.php?id=<?php echo $expense_id; ?>"
                                                   class="btn btn-sm btn-outline-primary"
                                                   title="View Expense Details">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <!-- Debug: Expense ID = <?php echo htmlspecialchars($expense_id); ?> -->
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <?php if (empty($expense_summary)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No expenses found for the selected criteria.</p>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        Debug: Found <?php echo count($receipt_data); ?> raw records<br>
                                        Transfer Amount Column: <?php echo $transfer_amount_exists ? 'EXISTS' : 'MISSING'; ?><br>
                                        <a href="../debug_table.php" class="btn btn-sm btn-outline-secondary">Debug Database</a>
                                    </small>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="mt-2">
                                <small class="text-muted">
                                    Debug: <?php echo count($expense_summary); ?> expenses, <?php echo count($receipt_data); ?> total records
                                    <?php if (!$transfer_amount_exists): ?>
                                        | <span class="text-warning">⚠️ transfer_amount column missing</span>
                                    <?php endif; ?>
                                </small>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
