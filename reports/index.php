<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/report_access_control.php';

// Check access
enforceReportAccess();

$database = new Database();
$db = $database->getConnection();

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['role'];

// Get filter parameters
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // Today
$status_filter = $_GET['status'] ?? '';
$user_filter = $_GET['user'] ?? '';
$export = $_GET['export'] ?? '';

// Build query based on user role and filters
$where_conditions = [];
$params = [];

// Role-based access
if ($user_role === 'data_entry') {
    $where_conditions[] = 'e.created_by = ?';
    $params[] = $user_id;
}
// report_viewer, verification, reviewer, and administrator can see all data

// Date filters
if (!empty($date_from)) {
    $where_conditions[] = 'e.job_open_date >= ?';
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = 'e.job_open_date <= ?';
    $params[] = $date_to;
}

// Status filter
if (!empty($status_filter)) {
    $where_conditions[] = 'e.status = ?';
    $params[] = $status_filter;
}

// User filter (only for admin and higher roles)
if (!empty($user_filter) && $user_role !== 'data_entry') {
    $where_conditions[] = 'e.created_by = ?';
    $params[] = $user_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get expenses data
$stmt = $db->prepare("
    SELECT 
        e.*,
        i.name as item_name,
        c.name as customer_name,
        d.name as driver_name,
        u.full_name as created_by_name
    FROM expenses e
    LEFT JOIN items i ON e.item_id = i.id
    LEFT JOIN customers c ON e.customer_id = c.id
    LEFT JOIN drivers d ON e.driver_id = d.id
    LEFT JOIN users u ON e.created_by = u.id
    $where_clause
    ORDER BY e.created_at DESC
");

$stmt->execute($params);
$expenses = $stmt->fetchAll();

// Get summary statistics
$summary_stmt = $db->prepare("
    SELECT
        COUNT(*) as total_count,
        SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open_count,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_count,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
        SUM(CASE WHEN status = 'returned' THEN 1 ELSE 0 END) as returned_count,
        SUM(CASE WHEN status = 'checked' THEN 1 ELSE 0 END) as check_count
    FROM expenses e
    $where_clause
");

$summary_stmt->execute($params);
$summary = $summary_stmt->fetch();

// Export to CSV if requested
if ($export === 'csv') {
    $filename = 'expenses_report_' . date('Y-m-d') . '.csv';
    
    $headers = [
        'Expense No', 'Booking No', 'Job Open Date', 'Item', 'Customer', 'Container No',
        'Driver', 'Vehicle Plate', 'Requester', 'Receiver', 'Payer', 'Withdrawal Date',
        'Transfer No', 'Status', 'Created By', 'Created At'
    ];
    
    $data = [];
    foreach ($expenses as $expense) {
        $data[] = [
            $expense['exno'],
            $expense['bookingno'],
            $expense['job_open_date'],
            $expense['item_name'],
            $expense['customer_name'],
            $expense['containerno'],
            $expense['driver_name'],
            $expense['vehicle_plate'],
            $expense['requester'],
            $expense['receiver'],
            $expense['payer'],
            $expense['withdrawal_date'],
            $expense['transfer_no'],
            ucfirst($expense['status']),
            $expense['created_by_name'],
            $expense['created_at']
        ];
    }
    
    exportToCSV($data, $filename, $headers);
}

// Get users for filter (admin only)
$users = [];
if ($user_role !== 'data_entry') {
    $users_stmt = $db->query("SELECT id, full_name FROM users WHERE is_active = 1 ORDER BY full_name");
    $users = $users_stmt->fetchAll();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - Expenses Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4"><i class="fas fa-chart-bar me-2"></i>Expenses Report</h1>

                <!-- Filters -->
                <div class="search-filter-bar">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">Date From</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">Date To</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Statuses</option>
                                <option value="open" <?php echo $status_filter === 'open' ? 'selected' : ''; ?>>Open</option>
                                <option value="checked" <?php echo $status_filter === 'checked' ? 'selected' : ''; ?>>Checked</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                <option value="success" <?php echo $status_filter === 'success' ? 'selected' : ''; ?>>Approved</option>
                                <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                            </select>
                        </div>
                        <?php if ($user_role !== 'data_entry'): ?>
                        <div class="col-md-2">
                            <label for="user" class="form-label">Created By</label>
                            <select class="form-select" id="user" name="user">
                                <option value="">All Users</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" 
                                            <?php echo $user_filter == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <?php endif; ?>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <a href="index.php" class="btn btn-outline-warning" title="Reset Filters">
                                    <i class="fas fa-undo"></i>
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Quick Reports -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Quick Reports</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <a href="receipt_summary.php" class="btn btn-outline-info w-100 mb-2">
                                            <i class="fas fa-receipt"></i> Receipt Summary Report
                                        </a>
                                    </div>
                                    <div class="col-md-4">
                                        <a href="../api/export_csv.php?<?php echo http_build_query($_GET); ?>" class="btn btn-outline-success w-100 mb-2">
                                            <i class="fas fa-download"></i> Export to CSV
                                        </a>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="window.print()">
                                            <i class="fas fa-print"></i> Print Report
                                        </button>
                                    </div>
                                </div>

                                <!-- CSV Export Options -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <a href="../api/export_csv.php?<?php echo http_build_query($_GET); ?>" class="btn btn-outline-success w-100 mb-2">
                                            <i class="fas fa-download"></i> CSV (Regular)
                                        </a>
                                    </div>
                                    <div class="col-md-6">
                                        <a href="../api/export_csv_per_receipt.php?<?php echo http_build_query($_GET); ?>" class="btn btn-success w-100 mb-2">
                                            <i class="fas fa-list"></i> CSV (Per Receipt)
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Summary Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">Total</h4>
                                        <h2><?php echo $summary['total_count']; ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-receipt fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">Open</h4>
                                        <h2><?php echo $summary['open_count']; ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-folder-open fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">Pending</h4>
                                        <h2><?php echo $summary['pending_count']; ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-clock fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">Approved</h4>
                                        <h2><?php echo $summary['success_count']; ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second Row of Status Cards -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">Rejected</h4>
                                        <h2><?php echo $summary['rejected_count']; ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-times-circle fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h4 class="card-title">Check Count</h4>
                                        <h2><?php echo $summary['check_count']; ?></h2>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="fas fa-check fa-2x"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Export Options -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>Expenses List (<?php echo count($expenses); ?> records)</h5>
                    <div>
                        <div class="btn-group me-2" role="group">
                            <a href="../api/export_csv.php?<?php echo http_build_query($_GET); ?>"
                               class="btn btn-outline-success btn-sm">
                                <i class="fas fa-download me-1"></i>CSV (Regular)
                            </a>
                            <a href="../api/export_csv_per_receipt.php?<?php echo http_build_query($_GET); ?>"
                               class="btn btn-success btn-sm">
                                <i class="fas fa-list me-1"></i>CSV (Per Receipt)
                            </a>
                        </div>
                        <button class="btn btn-outline-secondary btn-sm" onclick="window.print()">
                            <i class="fas fa-print me-1"></i>Print
                        </button>
                    </div>
                </div>

                <!-- Expenses Table -->
                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Expense No.</th>
                                        <th>Date</th>
                                        <th>Item</th>
                                        <th>Driver</th>
                                        <th>Transfer Amount</th>
                                        <th>Receipt Numbers</th>
                                        <th>Withdrawal Date</th>
                                        <th>Status</th>
                                        <th>Created By</th>
                                        <th class="no-print">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($expenses)): ?>
                                        <tr>
                                            <td colspan="10" class="text-center">No expenses found for the selected criteria</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($expenses as $expense): ?>
                                            <tr>
                                                <td><strong><?php echo htmlspecialchars($expense['exno']); ?></strong></td>
                                                <td><?php echo formatDate($expense['job_open_date']); ?></td>
                                                <td><?php echo htmlspecialchars($expense['item_name'] ?: '-'); ?></td>
                                                <td><?php echo htmlspecialchars($expense['driver_name'] ?: '-'); ?></td>
                                                <td class="text-end">
                                                    <?php if (isset($expense['transfer_amount']) && $expense['transfer_amount'] > 0): ?>
                                                        <span class="text-primary fw-bold"><?php echo number_format($expense['transfer_amount'], 2); ?></span>
                                                        <small class="text-muted d-block">บาท</small>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td style="max-width: 200px;">
                                                    <?php
                                                    // Get receipt numbers for this expense
                                                    $stmt = $db->prepare("SELECT receipt_number, amount FROM receipt_numbers WHERE expense_id = ? ORDER BY id");
                                                    $stmt->execute([$expense['id']]);
                                                    $receipts = $stmt->fetchAll();

                                                    if (!empty($receipts)):
                                                        $total_receipt_amount = 0;
                                                        foreach ($receipts as $receipt):
                                                            $total_receipt_amount += $receipt['amount'];
                                                    ?>
                                                        <div class="badge bg-light text-dark me-1 mb-1">
                                                            <?php echo htmlspecialchars($receipt['receipt_number']); ?>
                                                            <small class="text-success">(<?php echo number_format($receipt['amount'], 2); ?>)</small>
                                                        </div>
                                                    <?php endforeach; ?>
                                                        <div class="mt-1">
                                                            <small class="text-muted">
                                                                Total: <span class="text-success fw-bold"><?php echo number_format($total_receipt_amount, 2); ?> บาท</span>
                                                                <?php if (isset($expense['transfer_amount']) && abs($expense['transfer_amount'] - $total_receipt_amount) > 0.01): ?>
                                                                    <span class="text-warning">
                                                                        <i class="fas fa-exclamation-triangle"></i> Mismatch
                                                                    </span>
                                                                <?php endif; ?>
                                                            </small>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="text-muted">No receipts</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($expense['withdrawal_date'])): ?>
                                                        <?php echo formatDate($expense['withdrawal_date']); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo getStatusBadge($expense['status']); ?></td>
                                                <td><?php echo htmlspecialchars($expense['created_by_name']); ?></td>
                                                <td class="no-print">
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="../expenses/view.php?id=<?php echo $expense['id']; ?>" 
                                                           class="btn btn-outline-primary" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if (canEditExpense($expense, $user_id, $user_role)): ?>
                                                            <a href="../expenses/edit.php?id=<?php echo $expense['id']; ?>" 
                                                               class="btn btn-outline-secondary" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
